const path = require('path');

module.exports = {
    mode: 'production', // development | production
    devtool: 'source-map', // Enable source maps for debugging

    entry: {
        // Chrome extension content script entry point
        'code.bundle': './modules/sender.ts'
    },

    output: {
        path: path.resolve(__dirname, 'chrome'),
        filename: '[name].js',
        // Chrome extension compatible output
        library: {
            type: 'umd',
            name: 't2sExtension'
        },
        globalObject: 'this',
        clean: false // Don't clean chrome directory (preserve manifest.json, etc.)
    },

    resolve: {
        extensions: ['.ts', '.tsx', '.js', '.jsx'],
        alias: {
            '@': path.resolve(__dirname, './'),
            '@modules': path.resolve(__dirname, './modules'),
            '@chrome': path.resolve(__dirname, './chrome')
        }
    },

    module: {
        rules: [
            {
                test: /\.tsx?$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                ['@babel/preset-env', {
                                    targets: {
                                        chrome: '88' // Chrome extension minimum version
                                    },
                                    modules: false
                                }],
                                '@babel/preset-typescript'
                            ],
                            plugins: [
                                '@babel/plugin-proposal-class-properties',
                                '@babel/plugin-proposal-object-rest-spread'
                            ]
                        }
                    }
                ]
            },
            {
                test: /\.jsx?$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: [
                            ['@babel/preset-env', {
                                targets: {
                                    chrome: '88'
                                }
                            }]
                        ]
                    }
                }
            }
        ]
    },

    // Chrome extension specific optimizations
    optimization: {
        minimize: true,
        // Don't split chunks for Chrome extension
        splitChunks: false
    },

    // Ensure compatibility with Chrome extension CSP
    performance: {
        hints: false
    },

    target: 'web'
};