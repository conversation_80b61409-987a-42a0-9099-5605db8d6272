{"name": "t2s", "version": "1.0.0", "description": "Text-to-Speech Chrome Extension and Web Application", "type": "module", "main": "modules/sender.ts", "scripts": {"test": "jest", "build": "webpack --config webpack.config.cjs", "clean": "rm -f chrome/code.bundle.js chrome/code.bundle.js.map"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/preset-env": "^7.23.0", "@babel/preset-typescript": "^7.23.0", "@types/chrome": "^0.0.254", "@types/jest": "^29.5.14", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/react-router-dom": "^5.3.3", "babel-loader": "^9.1.3", "jasmine": "^5.1.0", "typescript": "^5.2.0", "webpack": "^5.87.0", "webpack-cli": "^5.1.4", "node-fetch": "^3.3.2", "ts-jest": "^29.2.5"}}