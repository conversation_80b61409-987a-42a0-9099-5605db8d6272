import TextSplitter from "./TextSplitter.ts";
import Paragraph from "./Paragraph.ts";

export async function start(baseUrl: string): Promise<void> {
    const selection: Selection | null = window.getSelection();
    const text: string = selection ? selection.toString() : '';

    if (!text.trim()) {
        console.warn('No text selected');
        return;
    }

    const lang: string = document.documentElement.lang || 'en';

    try {
        await storeText(baseUrl, toParagraphs(text));
        window.open(`${baseUrl}/?slotId=default&lang=${lang}`, '_blank');
    } catch (error) {
        console.error('Failed to start text-to-speech:', error);
    }
}

function storeText(baseUrl: string, paragraphs: Paragraph[]): Promise<Response | void> {
    const url: string = `${baseUrl}/api.php?cmd=store-json`;

    return fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(paragraphs)
    }).catch((error: Error) => {
        console.error('Error storing text:', error);
        throw error;
    });
}

export function toParagraphs(searchText: string): Paragraph[] {
    const result: Paragraph[] = [];

    if (!searchText.trim()) {
        return result;
    }

    // Escape special characters for XPath
    const escapedText: string = searchText.replace(/'/g, "\\'");

    let runner: Element | null = document.evaluate(
        `//*[contains(text(), '${escapedText}')]`,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
    ).singleNodeValue as Element | null;

    let c = 1;
    while (runner) {

        if (runner instanceof HTMLElement && runner.innerText?.trim()) {
            const paragraphId = `p_${c++}`;
            const sentences = new TextSplitter().toSentences(runner.innerText.trim(), paragraphId);
            result.push(new Paragraph(sentences, paragraphId));
        }

        runner = runner.nextElementSibling;
    }

    return result;
}

declare var window: any;

if (typeof window !== 'undefined') {
    window.t2sExtension = {
        start,
        toParagraphs
    };
}
