import React from 'react';
import PlayerControls from './PlayerControls.tsx';
import LanguageSelector from './LanguageSelector.tsx';
import ClipboardManager from './ClipboardManager.tsx';
import SlotManager from './SlotManager.tsx';
import TextDisplay from './TextDisplay.tsx';

import Repository from '../../modules/Repository.ts';
import SectionPlayer from '../../modules/SectionPlayer.ts';
import TextSplitter from '../../modules/TextSplitter.ts';
import Paragraph from '../../modules/Paragraph.ts';
import Sentence from '../../modules/Sentence.ts';
import { storeParagraphs, fetchParagraphs } from '../../modules/dao.ts';
import { Link } from "react-router-dom";

type Language = 'en' | 'no' | 'et';
type SlotId = 'default' | '1' | '2';

interface MainCompProps {
    repository: Repository;
    player: SectionPlayer;
}

interface MainCompState {
    paragraphs: Paragraph[];
    currentLanguage: Language;
    currentSlot: SlotId;
    sentenceId: string | undefined;
    currentSentenceId: string | undefined;
    isPlaying: boolean;
}

class MainComp extends React.Component<MainCompProps, MainCompState> {
    private textSplitter = new TextSplitter();
    private keyboardHandler?: (event: KeyboardEvent) => void;
    private playerUpdateHandler?: () => void;

    state: MainCompState = {
        paragraphs: [],
        currentLanguage: 'en',
        currentSlot: 'default',
        sentenceId: undefined,
        currentSentenceId: undefined,
        isPlaying: false
    };

    componentDidMount() {
        this.initializeComponent();
        this.setupKeyboardListeners();
        this.setupPlayerListeners();
    }

    componentDidUpdate(prevProps: MainCompProps, prevState: MainCompState) {
        if (prevState.currentLanguage !== this.state.currentLanguage) {
            this.props.player.setLang(this.state.currentLanguage);
        }
    }

    componentWillUnmount() {
        this.removeKeyboardListeners();
        this.removePlayerListeners();
    }

    private initializeComponent() {
        const paragraphs = this.props.repository.getParagraphs();
        const currentLanguage = this.props.repository.getLang() as Language;
        const sentenceId = this.props.repository.getSentenceId();
        const currentSlot = this.props.repository.getCurrentSlot() as SlotId;

        this.setState({
            paragraphs,
            currentLanguage,
            sentenceId,
            currentSlot
        });

        this.props.player.setParagraphs(paragraphs);
        this.props.player.setLang(currentLanguage);
        if (sentenceId) {
            this.props.player.goToSentence(sentenceId);
        }
    }

    private handleContentChange = (): void => {
        this.props.player.stop();
        const paragraphs = this.props.repository.getParagraphs();
        const sentenceId = this.props.repository.getSentenceId();
        const currentLanguage = this.props.repository.getLang() as Language;

        this.setState({ paragraphs, sentenceId, currentLanguage });

        this.props.player.setParagraphs(paragraphs);
        if (sentenceId) {
            this.props.player.goToSentence(sentenceId);
        }
        this.props.player.setLang(currentLanguage);
    };

    private setupKeyboardListeners(): void {
        this.keyboardHandler = (event: KeyboardEvent) => {
            if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
                return;
            }

            switch (event.code) {
                case 'Space':
                    event.preventDefault();
                    this.handlePlayPause();
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    this.handleBack();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    this.handleNext();
                    break;
            }
        };

        document.addEventListener('keydown', this.keyboardHandler);
    }

    private removeKeyboardListeners(): void {
        if (this.keyboardHandler) {
            document.removeEventListener('keydown', this.keyboardHandler);
        }
    }

    private setupPlayerListeners(): void {
        this.playerUpdateHandler = () => {
            this.setState({
                isPlaying: this.props.player.isPlaying(),
                currentSentenceId: this.props.player.getCurrentSentenceId()
            });
        };

        this.props.player.onUpdate(this.playerUpdateHandler);
    }

    private removePlayerListeners(): void {
        if (this.playerUpdateHandler) {
            this.props.player.offUpdate(this.playerUpdateHandler);
        }
    }

    // Handle sentence selection
    private handleSentenceSelected = (sentenceId: string): void => {
        this.props.repository.saveSentenceId(sentenceId);
        this.props.player.goToSentence(sentenceId);
        this.setState({ sentenceId });
    };

    // Audio control handlers
    private handlePlayPause = async (): Promise<void> => {
        await this.props.player.playPause();
    };

    private handleNext = async (): Promise<void> => {
        await this.props.player.next();
    };

    private handleBack = async (): Promise<void> => {
        await this.props.player.back();
    };

    // Event handlers
    private handleLanguageChange = (language: Language): void => {
        this.props.repository.selectLang(language);
        this.setState({ currentLanguage: language });
    };

    private handleSlotSave = async (slotId: SlotId): Promise<void> => {
        try {
            await storeParagraphs(this.state.paragraphs, slotId);
            this.props.repository.deleteSentenceId(slotId);
        } catch (error) {
            console.error('Failed to save to slot:', error);
        }
    };

    private handleSlotLoad = async (slotId: SlotId): Promise<void> => {
        try {
            this.props.repository.selectSlot(slotId);
            const paragraphs = await fetchParagraphs(slotId);
            this.props.repository.setParagraphs(paragraphs);
            this.setState({ currentSlot: slotId });
            this.handleContentChange();
        } catch (error) {
            console.error('Failed to load slot:', error);
        }
    };

    private handlePasteFromClipboard = (text: string): void => {
        const paragraphs = this.textSplitter.toParagraphs(text);
        this.props.repository.setParagraphs(paragraphs);
        this.props.repository.deleteSentenceId(this.props.repository.getCurrentSlot());
        this.setState({ paragraphs });
        this.handleContentChange();
    };

    private handlePasteFromClipboardCleaned = (sentences: string[]): void => {
        // Convert sentences to Sentence objects and create a single paragraph
        const sentenceObjects = sentences.map(
            (sentence, index) => new Sentence(sentence, `p1_s_${index + 1}`)
        );
        const paragraphs = [new Paragraph(sentenceObjects)];

        this.props.repository.setParagraphs(paragraphs);
        this.props.repository.deleteSentenceId(this.props.repository.getCurrentSlot());
        this.setState({ paragraphs });
        this.handleContentChange();
    };

    render() {
        return (
            <div>
                <nav>
                    <PlayerControls
                        isPlaying={this.state.isPlaying}
                        onPlayPause={this.handlePlayPause}
                        onNext={this.handleNext}
                        onBack={this.handleBack}
                    />

                    <ClipboardManager
                        onPasteFromClipboard={this.handlePasteFromClipboard}
                        onPasteFromClipboardCleaned={this.handlePasteFromClipboardCleaned}
                    />

                    &nbsp;
                    <Link to="/pdf" id="pdf-link">Pdf</Link>
                    &nbsp;
                    <a href="" id="settings-link">Settings</a>
                    &nbsp;

                    <LanguageSelector
                        selectedLanguage={this.state.currentLanguage}
                        onLanguageChange={this.handleLanguageChange}
                    />

                    <SlotManager
                        currentSlot={this.state.currentSlot}
                        onSlotSave={this.handleSlotSave}
                        onSlotLoad={this.handleSlotLoad}
                    />
                </nav>

                <TextDisplay
                    paragraphs={this.state.paragraphs}
                    highlightedSentenceId={this.state.currentSentenceId}
                    onSentenceSelect={this.handleSentenceSelected}
                />
            </div>
        );
    }
}

export default MainComp;
