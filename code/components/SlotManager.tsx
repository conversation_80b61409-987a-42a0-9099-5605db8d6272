import React from 'react';

type SlotId = 'default' | '1' | '2';

interface SlotManagerProps {
    currentSlot: SlotId;
    onSlotSave: (slotId: SlotId) => void;
    onSlotLoad: (slotId: SlotId) => void;
}

const SlotManager: React.FC<SlotManagerProps> = ({
    currentSlot,
    onSlotSave,
    onSlotLoad
}) => {
    const slots: { id: SlotId; loadLabel: string; saveLabel: string }[] = [
        { id: 'default', loadLabel: 'LD', saveLabel: 'SD' },
        { id: '1', loadLabel: 'L1', saveLabel: 'S1' },
        { id: '2', loadLabel: 'L2', saveLabel: 'S2' }
    ];

    const handleSlotLoad = (event: React.MouseEvent<HTMLAnchorElement>, slotId: SlotId) => {
        event.preventDefault();
        onSlotLoad(slotId);
    };

    const handleSlotSave = (event: React.MouseEvent<HTMLAnchorElement>, slotId: SlotId) => {
        event.preventDefault();
        onSlotSave(slotId);
    };

    return (
        <div id="slots">
            {slots.map(({ id, loadLabel, saveLabel }) => (
                <div key={id}>
                    <a
                        href=""
                        className={currentSlot === id ? 'active' : ''}
                        data-slot-type="load"
                        data-slot-id={id}
                        onClick={(e) => handleSlotLoad(e, id)}
                    >
                        {loadLabel}
                    </a>
                    <br />
                    <a
                        href=""
                        data-slot-type="save"
                        data-slot-id={id}
                        onClick={(e) => handleSlotSave(e, id)}
                    >
                        {saveLabel}
                    </a>
                </div>
            ))}
        </div>
    );
};

export default SlotManager;
