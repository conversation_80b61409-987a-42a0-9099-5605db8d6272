import React from 'react';

interface PlayerControlsProps {
    isPlaying: boolean;
    onPlayPause: () => void;
    onNext: () => void;
    onBack: () => void;
}

const PlayerControls: React.FC<PlayerControlsProps> = ({
    isPlaying,
    onPlayPause,
    onNext,
    onBack
}) => {
    return (
        <div className="player-controls">
            <button 
                id="play-pause-button"
                onClick={onPlayPause}
                dangerouslySetInnerHTML={{ __html: isPlaying ? 'II' : '&gt;' }}
            />
            <button 
                id="back-button"
                onClick={onBack}>&lt;&lt;</button>
            <button 
                id="next-button"
                onClick={onNext}>&gt;&gt;</button>
        </div>
    );
};

export default PlayerControls;
