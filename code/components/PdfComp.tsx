import React from 'react';
import { Link } from 'react-router-dom';
import { fetchPdfPage } from '../../modules/dao.ts';
import Repository from '../../modules/Repository.ts';
import SectionPlayer from '../../modules/SectionPlayer.ts';
import Paragraph from '../../modules/Paragraph.ts';
import TextSplitter from '../../modules/TextSplitter.ts';
import PlayerControls from './PlayerControls.tsx';
import LanguageSelector from './LanguageSelector.tsx';
import TextDisplay from './TextDisplay.tsx';

type Language = 'en' | 'no' | 'et';

interface PdfCompProps {
    repository: Repository;
    player: SectionPlayer;
}

interface PdfCompState {
    currentPage: number;
    pageInput: string;
    content: string;
    paragraphs: Paragraph[];
    error: string | null;
    currentSentenceId: string | undefined;
    isPlaying: boolean;
    currentLanguage: Language;
}

class PdfComp extends React.Component<PdfCompProps, PdfCompState> {
    private textSplitter = new TextSplitter();
    private keyboardHandler?: (event: KeyboardEvent) => void;
    private playerUpdateHandler?: () => void;

    state: PdfCompState = {
        currentPage: 1,
        pageInput: '1',
        content: '',
        paragraphs: [],
        error: null,
        currentSentenceId: undefined,
        isPlaying: false,
        currentLanguage: 'en'
    };

    componentDidMount() {
        this.initializeComponent();
        this.setupKeyboardListeners();
        this.setupPlayerListeners();
    }

    componentDidUpdate(prevProps: PdfCompProps, prevState: PdfCompState) {
        if (prevState.currentPage !== this.state.currentPage) {
            this.loadPage(this.state.currentPage);
        }

        if (prevState.currentLanguage !== this.state.currentLanguage) {
            this.props.player.setLang(this.state.currentLanguage);
        }
    }

    componentWillUnmount() {
        this.removeKeyboardListeners();
        this.removePlayerListeners();
    }

    private initializeComponent() {
        const savedPage = this.props.repository.getPdfPage();
        const currentLanguage = this.props.repository.getLang() as Language;

        this.setState({
            currentPage: savedPage,
            pageInput: savedPage.toString(),
            currentLanguage
        });

        this.props.player.setLang(currentLanguage);
        this.loadPage(savedPage);
    }

    private setupKeyboardListeners(): void {
        this.keyboardHandler = (event: KeyboardEvent) => {
            if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
                return;
            }

            switch (event.code) {
                case 'Space':
                    event.preventDefault();
                    this.handlePlayPause();
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    this.handleBack();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    this.handleNext();
                    break;
            }
        };

        document.addEventListener('keydown', this.keyboardHandler);
    }

    private removeKeyboardListeners(): void {
        if (this.keyboardHandler) {
            document.removeEventListener('keydown', this.keyboardHandler);
        }
    }

    private setupPlayerListeners(): void {
        this.playerUpdateHandler = () => {
            this.setState({
                isPlaying: this.props.player.isPlaying(),
                currentSentenceId: this.props.player.getCurrentSentenceId()
            });
        };

        this.props.player.onUpdate(this.playerUpdateHandler);
    }

    private removePlayerListeners(): void {
        if (this.playerUpdateHandler) {
            this.props.player.offUpdate(this.playerUpdateHandler);
        }
    }

    private loadPage = async (page: number): Promise<void> => {
        this.setState({ error: null });

        try {
            const pageContent = await fetchPdfPage(page);

            // Process content into paragraphs for audio playback
            const processedParagraphs = this.textSplitter.toParagraphs(pageContent);

            this.setState({
                content: pageContent,
                currentPage: page,
                pageInput: page.toString(),
                paragraphs: processedParagraphs
            });

            // Update player with new content
            this.props.player.setParagraphs(processedParagraphs);

            // Restore sentence position for this page
            const savedSentenceId = this.props.repository.getPdfSentenceId(page);
            if (savedSentenceId) {
                this.props.player.goToSentence(savedSentenceId);
            }

            // Save page to repository
            this.props.repository.savePdfPage(page);
        } catch (err) {
            this.setState({
                error: err instanceof Error ? err.message : 'Failed to load PDF page',
                content: '',
                paragraphs: []
            });
        }
    };

    // Audio control handlers
    private handlePlayPause = (): void => {
        this.props.player.playPause();
    };

    private handleNext = (): void => {
        this.props.player.next();
    };

    private handleBack = (): void => {
        this.props.player.back();
    };

    // Page navigation handlers
    private handlePageInputChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
        this.setState({ pageInput: event.target.value });
    };

    private handlePageInputSubmit = (event: React.FormEvent): void => {
        event.preventDefault();
        const page = parseInt(this.state.pageInput, 10);

        if (isNaN(page) || page < 1) {
            this.setState({
                error: 'Please enter a valid page number (1 or greater)',
                pageInput: this.state.currentPage.toString()
            });
            return;
        }

        this.setState({ currentPage: page });
    };

    private handlePreviousPage = (): void => {
        if (this.state.currentPage > 1) {
            this.setState({ currentPage: this.state.currentPage - 1 });
        }
    };

    private handleNextPage = (): void => {
        this.setState({ currentPage: this.state.currentPage + 1 });
    };

    private handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>): void => {
        if (event.key === 'Enter') {
            this.handlePageInputSubmit(event);
        }
    };

    private handleSentenceSelected = (sentenceId: string): void => {
        this.props.repository.savePdfSentenceId(this.state.currentPage, sentenceId);
        this.props.player.goToSentence(sentenceId);
        this.setState({ currentSentenceId: sentenceId });
    };

    private handleLanguageChange = (language: Language): void => {
        this.props.repository.selectLang(language);
        this.setState({ currentLanguage: language });
    };

    render() {
        return (
            <div id="pdf-container">
                <nav>
                    <div>
                        <button
                            onClick={this.handlePreviousPage}
                            disabled={this.state.currentPage <= 1}
                        >
                            &lt;
                        </button>

                        <form onSubmit={this.handlePageInputSubmit} style={{ display: 'inline' }}>
                            <input
                                id="pdf-page-no-input"
                                type="text"
                                value={this.state.pageInput}
                                onChange={this.handlePageInputChange}
                                onKeyDown={this.handleKeyDown}
                                placeholder="Page"
                            />
                        </form>

                        <button
                            onClick={this.handleNextPage}
                        >
                            &gt;
                        </button>
                    </div>

                    &nbsp;

                    <PlayerControls
                        isPlaying={this.state.isPlaying}
                        onPlayPause={this.handlePlayPause}
                        onNext={this.handleNext}
                        onBack={this.handleBack}
                    />

                    &nbsp;

                    <LanguageSelector
                        selectedLanguage={this.state.currentLanguage}
                        onLanguageChange={this.handleLanguageChange}
                    />

                    &nbsp;
                    <Link to="/">Main</Link>
                </nav>

                {this.state.error && <div>Error: {this.state.error}</div>}
                {!this.state.error && this.state.paragraphs.length > 0 && (
                    <TextDisplay
                        paragraphs={this.state.paragraphs}
                        highlightedSentenceId={this.state.currentSentenceId}
                        onSentenceSelect={this.handleSentenceSelected}
                    />
                )}
                {!this.state.error && this.state.paragraphs.length === 0 && this.state.content && (
                    <div>No content found for page {this.state.currentPage}</div>
                )}
            </div>
        );
    }
}

export default PdfComp;
