import React from 'react';
import cleanUpText from '../../modules/textCleanerApi.ts';

interface ClipboardManagerProps {
    onPasteFromClipboard: (text: string) => void;
    onPasteFromClipboardCleaned: (sentences: string[]) => void;
}

const ClipboardManager: React.FC<ClipboardManagerProps> = ({
    onPasteFromClipboard,
    onPasteFromClipboardCleaned
}) => {
    const handlePasteFromClipboard = async () => {
        try {
            const text = await navigator.clipboard.readText();
            onPasteFromClipboard(text);
        } catch (error) {
            console.error('Failed to read clipboard:', error);
        }
    };

    const handlePasteFromClipboardCleaned = async () => {
        try {
            const text = await navigator.clipboard.readText();
            const sentences = await cleanUpText(text);
            onPasteFromClipboardCleaned(sentences);
        } catch (error) {
            console.error('Failed to clean clipboard text:', error);
        }
    };

    return (
        <div className="clipboard-manager">
            <button 
                id="clipboard-button"
                onClick={handlePasteFromClipboard}
            >
                From Clipboard
            </button>
            <button 
                id="clipboard-cleaned-button"
                onClick={handlePasteFromClipboardCleaned}
            >
                From Clipboard cleaned
            </button>
        </div>
    );
};

export default ClipboardManager;
