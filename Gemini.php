<?php

class Gemini {

    private string $apiKey;
    private string $modelName;
    private string $systemInstruction;
    private array $generationConfig;

    public function __construct(string $apiKey, string $modelName = 'gemini-2.5-flash') {
        $this->apiKey = $apiKey;
        $this->modelName = $modelName;

        // Default system instruction for sentence splitting
        $this->systemInstruction = 'Split the provided text into sentences that are separated by ||| symbols.

<example-input>
This sentence contains punctuation 1. marks and numbers. This sentence contains punctuation 1. marks and letters.
</example-input>

<expected-result>
This sentence contains punctuation 1. marks and numbers. ||| This sentence contains punctuation 1. marks and letters.
</expected-result>';

        // Default generation configuration
        $this->generationConfig = [
            'temperature' => 0,
            'maxOutputTokens' => 4000
        ];
    }

    private function buildRequestBody(string $text): array {
        return [
            'system_instruction' => [
                'parts' => [
                    'text' => $this->systemInstruction
                ]
            ],
            'contents' => [
                'parts' => [
                    'text' => 'The text is "' . $text . '"'
                ]
            ],
            'generationConfig' => $this->generationConfig
        ];
    }

    private function parseResponse(array $response): string {
        // Check for API errors
        if (isset($response['error'])) {
            $errorMessage = $response['error']['message'] ?? 'Unknown API error';
            $errorCode = $response['error']['code'] ?? 'Unknown';
            throw new Exception("Gemini API error [{$errorCode}]: {$errorMessage}");
        }

        // Check if candidates exist
        if (!isset($response['candidates']) || !is_array($response['candidates']) || empty($response['candidates'])) {
            throw new Exception('No candidates found in API response');
        }

        // Get the first candidate
        $candidate = $response['candidates'][0];

        // Check if content exists
        if (!isset($candidate['content'])) {
            throw new Exception('No content found in candidate response');
        }

        // Extract text from parts
        if (isset($candidate['content']['parts']) && is_array($candidate['content']['parts'])) {
            $textParts = [];
            foreach ($candidate['content']['parts'] as $part) {
                if (isset($part['text'])) {
                    $textParts[] = $part['text'];
                }
            }

            if (empty($textParts)) {
                throw new Exception('No text parts found in response');
            }

            return implode('', $textParts);
        }

        throw new Exception('Invalid response format: missing or invalid parts structure');
    }

    private function sendRequestWithFileGetContents(array $requestBody): array {
        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->modelName}:generateContent?key={$this->apiKey}";

        $jsonData = json_encode($requestBody);
        if ($jsonData === false) {
            throw new Exception('Failed to encode request body as JSON');
        }

        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($jsonData)
                ],
                'content' => $jsonData,
                'timeout' => 30
            ]
        ]);

        $response = file_get_contents($url, false, $context);

        if ($response === false) {
            throw new Exception('HTTP request failed using file_get_contents');
        }

        $decodedResponse = json_decode($response, true);
        if ($decodedResponse === null) {
            throw new Exception("Failed to decode JSON response: {$response}");
        }

        return $decodedResponse;
    }

    public function makeQuery(string $text): string {
        $requestBody = $this->buildRequestBody($text);
        $response = $this->sendRequestWithFileGetContents($requestBody);
        return $this->parseResponse($response);
    }
}