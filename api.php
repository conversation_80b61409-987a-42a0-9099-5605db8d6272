<?php

require_once 'Dao.php';

$cmd = $_GET['cmd'];
$slotId = $_GET['slotId'] ?? 'default';
$dao = new Dao();

if ($cmd === 'code') {

    $path = './modules/' . $_GET['path'];

    sendCorsHeaders();

    header('Content-Type: application/javascript');

    $contents = file_get_contents($path);

    print rewriteImportStatements($contents);

} else if ($cmd === 'api-key') {

    sendCorsHeaders();

    header('Content-Type: application/json');

    print file_get_contents('settings.json');

} else if ($cmd === 'store-json') {

    sendCorsHeaders();

    $json = file_get_contents('php://input');

    error_log($json);

    $partObject = json_decode($json) ?? new stdClass();

    $dao->storeData($partObject, $slotId);

} else if ($cmd === 'fetch-json') {

    header('Content-Type: application/json');

    print json_encode($dao->fetchData($slotId));

} else if ($cmd === 'pdf') {

    sendCorsHeaders();

    $page = $_GET['page'] ?? 1;

    // Validate page number
    if (!is_numeric($page) || $page < 1) {
        http_response_code(400);
        header('Content-Type: text/plain');
        print "Invalid page number. Page must be a positive integer.";
        exit;
    }

    $page = (int)$page;
    $cmd = "pdftotext -f $page -l $page data/test.pdf -";

    exec($cmd, $output, $exitCode);

    if ($exitCode !== 0) {
        http_response_code(500);
        header('Content-Type: text/plain');
        print "Error reading PDF page $page. Page may not exist.";
        exit;
    }

    header('Content-Type: text/plain');
    print implode("\n", $output);

} else {
    throw new RuntimeException('Unknown command');
}

function rewriteImportStatements($script): string {
    $regex = '/^import\s+(.*?)\s+from\s+"(\.\/[^"\']+)"/m';

    return preg_replace_callback($regex, function ($matches) {
        $fullMatch = $matches[0];
        $filePath = $matches[2];
        return str_replace($filePath, './api.php?cmd=code&path=' . $filePath, $fullMatch);

    }, $script);

}

function sendCorsHeaders(): void {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
}
