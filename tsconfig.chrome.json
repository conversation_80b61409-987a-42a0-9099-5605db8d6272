{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": false, "sourceMap": true, "outDir": "./chrome", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./*"], "@modules/*": ["./modules/*"], "@chrome/*": ["./chrome/*"]}, "typeRoots": ["./node_modules/@types", "./chrome/types.d.ts"], "types": ["chrome"]}, "include": ["modules/**/*", "chrome/**/*", "chrome/types.d.ts"], "exclude": ["node_modules", "chrome/code.bundle.js", "chrome/code.bundle.js.map", "**/*.test.*", "**/*.spec.*"]}