<?php

const DATA_FILE = 'data/data.json';

//$dao = new Dao();
//$data = json_decode('[{"a": 1, "b": 2}]');
//$dao->storeData($data, 'default');
//var_dump($dao->fetchData('default'));

class Dao {

    function storeData(mixed $data, string $slotId): void {
        $dict = $this->fetchRawData();

        $dict->{$slotId} = $data;

                            // dict is encoded as an object
        file_put_contents(DATA_FILE, json_encode($dict));
    }

    function fetchRawData(): stdClass {
        $json = file_get_contents(DATA_FILE);

        if (!$json) {
            return new stdClass();
        }

        return json_decode($json);
    }

    function fetchData(string $slotId): array {
        return $this->fetchRawData()->{$slotId} ?? [];
    }
}