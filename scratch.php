<?php

require_once 'Gemini.php';

try {
    $apiKey = json_decode(file_get_contents('settings.json'), true)['api-key-gemini'];

    $text = "How serious? One of my patients stalked <PERSON><PERSON><PERSON>, and another jumped off a fifth-story balcony because he thought he could fly . Still another called me from a jail in the Dominican Republic, saying he was there to start a revolution . In addition, I’ve worked with 80-pound anorexics, strung-out heroin addicts, and hallucinating schizophrenics .";

    $gemini = new Gemini($apiKey);

    $result = $gemini->makeQuery($text);

    echo "Generated response:\n";
    echo $result . "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
