# T2S Chrome Extension

A Chrome extension that enables text-to-speech functionality by extracting selected text from web pages and opening it in the T2S web application.

## Features

- Extract selected text from any web page
- Process text into sentences for optimal speech synthesis
- Keyboard shortcuts for quick access
- Integration with the main T2S web application
- TypeScript-based with proper type safety

## Installation

### Development Installation

1. **Build the Extension**:
   ```bash
   cd /path/to/t2s
   npm install
   npm run build:chrome
   ```

2. **Load in Chrome**:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the `/chrome` directory from this project

3. **Verify Installation**:
   - The T2S icon should appear in the Chrome toolbar
   - Open `chrome/test.html` to test functionality

## Usage

### Method 1: Keyboard Shortcut
1. Select text on any web page
2. Press `Alt+Shift+O` to open the T2S player with selected text

### Method 2: Extension Icon
1. Select text on any web page
2. Click the T2S extension icon in the Chrome toolbar

### Method 3: Context Menu (Future)
- Right-click on selected text → "Speak with T2S" (to be implemented)

## Configuration

### API URL
The extension is configured to connect to `http://localhost:8000` by default. To change this:

1. Edit `chrome/background.js`
2. Modify the `API_URL` constant
3. Reload the extension in Chrome

### Keyboard Shortcuts
Default shortcuts can be customized in Chrome:
- Go to `chrome://extensions/shortcuts`
- Find "T2S" extension
- Modify shortcuts as needed

Available commands:
- **Open player page**: `Alt+Shift+O` - Extract selected text and open T2S player
- **Speak selected text**: `Alt+Shift+S` - Direct speech (future feature)

## Development

### Build Commands

```bash
# Production build (minified)
npm run build:chrome

# Development build (with source maps)
npm run build:dev

# Watch mode for development
npm run watch

# Clean build artifacts
npm run clean
```

### File Structure

```
chrome/
├── manifest.json          # Extension manifest (Manifest V3)
├── background.js          # Service worker (background script)
├── code.bundle.js         # Compiled TypeScript bundle
├── code.bundle.js.map     # Source map for debugging
├── types.d.ts            # TypeScript definitions for Chrome APIs
├── icon-play.png         # Extension icon
├── test.html            # Test page for development
└── README.md            # This file
```

### TypeScript Development

The extension is built from TypeScript source files:

- **Source**: `modules/sender.ts` - Main extension logic
- **Build**: `webpack.config.cjs` - Webpack configuration
- **Types**: `chrome/types.d.ts` - Chrome extension API types

### Testing

1. **Manual Testing**:
   - Open `chrome/test.html` in a browser
   - Test text selection and processing
   - Verify extension code loading

2. **Extension Testing**:
   - Load extension in Chrome
   - Test on various websites
   - Check browser console for errors

## Architecture

### Content Script Injection
The extension uses Chrome's `scripting` API to inject code into web pages:

1. User triggers extension (shortcut/icon)
2. Background script injects `code.bundle.js` into active tab
3. Injected code extracts selected text
4. Text is processed and sent to T2S API
5. T2S player opens in new tab

### Security
- Follows Chrome Extension Manifest V3 requirements
- No `eval()` or inline scripts (CSP compliant)
- Minimal permissions requested
- Secure communication with T2S API

## Troubleshooting

### Extension Not Loading
- Check Chrome developer mode is enabled
- Verify all files are present in `/chrome` directory
- Check Chrome console for errors

### Text Not Extracted
- Ensure text is properly selected
- Check if website blocks content scripts
- Verify API URL is accessible

### Build Errors
- Run `npm install` to ensure dependencies
- Check Node.js version compatibility
- Verify TypeScript compilation

## API Integration

The extension communicates with the T2S web application via:

- **Endpoint**: `${API_URL}/api.php?cmd=store-json`
- **Method**: POST
- **Data**: JSON array of paragraph objects
- **Response**: Success/error status

## Browser Compatibility

- **Chrome**: 88+ (Manifest V3 support)
- **Edge**: 88+ (Chromium-based)
- **Other browsers**: Not supported (Chrome extension specific)

## License

Same as main T2S project.
