<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome Extension Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-content {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #45a049;
        }
        #output {
            background: #fff;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>Chrome Extension Test Page</h1>
    
    <div class="instructions">
        <h3>Testing Instructions:</h3>
        <ol>
            <li>Load the Chrome extension from the <code>/chrome</code> directory</li>
            <li>Select some text from the content below</li>
            <li>Use the keyboard shortcut <strong>Alt+Shift+O</strong> to open the player page</li>
            <li>Or click the extension icon in the toolbar</li>
        </ol>
    </div>

    <div class="test-content">
        <h2>Sample Text for Testing</h2>
        <p>This is the first paragraph of sample text. It contains multiple sentences that can be used for testing the text-to-speech functionality. The extension should be able to extract this text and process it into individual sentences.</p>
        
        <p>Here is a second paragraph with different content. This paragraph also contains several sentences. Each sentence should be properly identified and separated by the text processing system.</p>
        
        <p>The third paragraph includes some punctuation marks! Does it handle question marks? What about exclamation points! These should all be properly recognized as sentence boundaries.</p>
    </div>

    <div>
        <h3>Manual Testing:</h3>
        <button onclick="testExtensionCode()">Test Extension Code</button>
        <button onclick="testTextSelection()">Test Text Selection</button>
        <button onclick="clearOutput()">Clear Output</button>
        
        <div id="output"></div>
    </div>

    <script src="code.bundle.js"></script>
    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function testExtensionCode() {
            log('Testing extension code availability...');
            
            if (window.t2sExtension) {
                log('✅ Extension code loaded successfully');
                log('Available functions: ' + Object.keys(window.t2sExtension).join(', '));
            } else {
                log('❌ Extension code not found');
            }
        }

        function testTextSelection() {
            log('Testing text selection and processing...');
            
            const selection = window.getSelection();
            const selectedText = selection ? selection.toString() : '';
            
            if (!selectedText.trim()) {
                log('⚠️ No text selected. Please select some text first.');
                return;
            }
            
            log('Selected text: "' + selectedText.substring(0, 100) + (selectedText.length > 100 ? '...' : '') + '"');
            
            if (window.t2sExtension && window.t2sExtension.toParagraphs) {
                try {
                    const paragraphs = window.t2sExtension.toParagraphs(selectedText);
                    log('✅ Text processed into ' + paragraphs.length + ' paragraph(s)');
                    
                    paragraphs.forEach((paragraph, index) => {
                        log(`Paragraph ${index + 1}: ${paragraph.sentences ? paragraph.sentences.length : 0} sentences`);
                    });
                } catch (error) {
                    log('❌ Error processing text: ' + error.message);
                }
            } else {
                log('❌ Extension toParagraphs function not available');
            }
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            setTimeout(testExtensionCode, 500);
        });
    </script>
</body>
</html>
