
// const API_URL = 'http://localhost:8080';
const API_URL = 'http://localhost/t2s';

function startShowPageCode(apiUrl) {
    if (window.t2sExtension && typeof window.t2sExtension.start === 'function') {
        return window.t2sExtension.start(apiUrl);
    } else {
        console.error('T2S Extension code not loaded. Make sure code.bundle.js is properly injected.');
        return Promise.reject('Extension code not available');
    }
}

async function injectExtensionCode(tabId) {
    try {
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['code.bundle.js']
        });

        console.log('Extension code injected successfully');
        return true;
    } catch (error) {
        console.error('Failed to inject extension code:', error);
        return false;
    }
}

// Event Listeners
chrome.action.onClicked.addListener(async (tab) => {
    console.log('Extension icon clicked for tab:', tab.id);

    if (tab.id) {
        await showPage(tab.id);
    }
});

chrome.commands.onCommand.addListener(async (command, tab) => {
    console.log('Command received:', command);

    if (command === "speak_selected_text") {
        if (tab?.id) {
            await speakSelectedText(tab.id);
        }
    } else if (command === "open_player_page") {
        if (tab?.id) {
            await showPage(tab.id);
        }
    }
});

async function showPage(tabId) {
    if (!tabId) {
        tabId = await getActiveTabId();
    }

    if (!tabId) {
        console.error('No active tab found');
        return;
    }

    try {
        const injected = await injectExtensionCode(tabId);

        if (!injected) {
            console.error('Failed to inject extension code');
            return;
        }

        await new Promise(resolve => setTimeout(resolve, 100));

        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: startShowPageCode,
            args: [API_URL],
        });

        console.log('T2S player page opened successfully');
    } catch (error) {
        console.error('Error showing page:', error);
    }
}

async function speakSelectedText(tabId) {
    try {
        const injected = await injectExtensionCode(tabId);

        if (!injected) {
            console.error('Failed to inject extension code');
            return;
        }

        console.log('Speak selected text feature - to be implemented');

        await showPage(tabId);
    } catch (error) {
        console.error('Error speaking selected text:', error);
    }
}

async function getActiveTabId() {
    try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        return tabs[0]?.id;
    } catch (error) {
        console.error('Error getting active tab:', error);
        return null;
    }
}

chrome.runtime.onInstalled.addListener((details) => {
    console.log('T2S Extension installed/updated:', details.reason);
});
