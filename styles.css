
body {
    font-size: larger;
    width: 50rem;
    margin: 0 auto;
}

nav {
    position: fixed;
    height: 2rem;
    padding-top: 0.5rem;
    background-color: white;
    width: 50rem;
}

nav > div {
    display: inline-block;
    margin-right: 0.5rem;
}

nav button {
    margin-right: 0.15rem;
}

nav a {
    color: gray;
    text-decoration: none;
    font-family: sans-serif;
    font-size: 1rem;
}

nav a:hover {
    color: black;
}

nav a.active {
    color: black;
}

#slots {
    display: grid;
    grid-template-columns: auto auto auto;
    grid-column-gap: 0.5rem;
    justify-content: space-between;
    float: right;
}

#pdf-page-no-input {
    width: 2rem;
}

#result-table {
    display: table;
    padding-top: 2rem;
}

#result-table > div {
    display: table-row;
}

#result-table > div > div {
    display: table-cell;
    padding: 0.5rem;
    border: 1px solid lightgray;
}

#play-pause-button {
    width: 3rem;
}